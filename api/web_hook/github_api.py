import os
import json
import hmac
import re
import logging
import ssl
import aiohttp
import requests
import xml.etree.ElementTree as ET
from typing import Dict, Any, List, Tuple
from fastapi import HTTPException, Request, BackgroundTasks
from fastapi.responses import JSONResponse
from api.web_hook.github_models import GithubPushEvent
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Import the FastAPI app instance
from api.web_hook.github_prompts import generate_wiki_structure_prompt

from dotenv import load_dotenv
from api.data_pipeline import DatabaseManager

load_dotenv()

app = FastAPI(
    title="Github Wehook API",
    description="API for webhooks"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)


# Configure logger
logger = logging.getLogger(__name__)

async def generate_wiki_structure(owner: str, repo: str, file_tree: str, readme: str) -> str:
    """
    Generate the wiki structure XML using an LLM or stub.
    Parameters:
        owner (str): Repository owner
        repo (str): Repository name
        file_tree (str): File tree as string
        readme (str): README content
    Returns:
        str: XML string representing the wiki structure
    """
    # For now, simulate LLM response for testing
    # In production, call the LLM API here
    return f"""
<wiki_structure>
  <title>{repo} Wiki</title>
  <description>Auto-generated wiki for {owner}/{repo}</description>
  <pages>
    <page id=\"page-1\">
      <title>Overview</title>
      <description>Project overview</description>
      <importance>high</importance>
      <relevant_files>
        <file_path>README.md</file_path>
      </relevant_files>
      <related_pages></related_pages>
    </page>
    <page id=\"page-2\">
      <title>Architecture</title>
      <description>System architecture</description>
      <importance>high</importance>
      <relevant_files>
        <file_path>src/main.py</file_path>
      </relevant_files>
      <related_pages></related_pages>
    </page>
  </pages>
</wiki_structure>
"""
 

def parse_wiki_structure(xml_text: str) -> Tuple[str, str, List[dict]]:
    """
    Parse the XML wiki structure and extract title, description, and pages.
    Parameters:
        xml_text (str): XML string
    Returns:
        Tuple[str, str, List[dict]]: (title, description, pages)
    """
    root = ET.fromstring(xml_text)
    title = root.findtext('title', default='')
    description = root.findtext('description', default='')
    pages = []
    for page_el in root.findall('.//page'):
        page = {
            'id': page_el.get('id', ''),
            'title': page_el.findtext('title', default=''),
            'description': page_el.findtext('description', default=''),
            'importance': page_el.findtext('importance', default='medium'),
            'file_paths': [fp.text for fp in page_el.findall('.//file_path') if fp.text],
            'related_pages': [rel.text for rel in page_el.findall('.//related') if rel.text],
        }
        pages.append(page)
    return title, description, pages
 

async def generate_page_content(page: dict, owner: str, repo: str) -> str:
    """
    Generate content for a wiki page (stub/LLM call).
    Parameters:
        page (dict): Page info
        owner (str): Repository owner
        repo (str): Repository name
    Returns:
        str: Markdown content for the page
    """
    # Simulate content generation
    return f"# {page['title']}\n\nThis is the auto-generated content for {page['title']} in {owner}/{repo}.\n\nSources: {', '.join(page['file_paths'])}"
 

async def process_github_repository_async(github_event: GithubPushEvent, actor_name: str = None):
    """
    Process a Github repository asynchronously to generate wiki documentation and content.
    Parameters:
        github_event (GithubpushEvent): Github push event information
        actor_name (str, optional): Name of the user who triggered the webhook
    Returns:
        dict: Result containing wiki structure and generated pages
    """
    try:
        repo_url = github_event.repository.html_url
        logger.info(f"Processing Github repository: {repo_url}")
        repo_parts = github_event.repository.full_name.split('/')
        if len(repo_parts) != 2:
            logger.error(f"Invalid repository full_name format: {github_event.repository.full_name}")
            return
        owner, repo = repo_parts

        # Create the repository structure
        database_manager = DatabaseManager()
        database_manager._create_repo(repo_url, "github")
        repo_location = database_manager.repo_paths["save_repo_dir"]
        logger.info(f"Saved the repo at {repo_location}")

        # Fetch file tree and README - fetchRepositoryUrl
        file_tree = await get_repo_file_tree(owner, repo, github_event.repository.default_branch)
        readme_content = await get_repo_readme(owner, repo)
        logger.info(f"First 100 chars of README for {owner}/{repo}: {readme_content[:100]}")
        
        logger.info(f"Starting async wiki generation for Github repository: {owner}/{repo}")
        # Use the generate_github_wiki_structure_prompt function to generate the request body
        repo_url = f"https://github.org/{owner}/{repo}"
        # Prepare request body for wiki structure generation
        # Import the chat completion function
        from api.simple_chat import chat_completions, ChatCompletionRequest, ChatMessage

        # Create a proper ChatCompletionRequest object
        chat_request = ChatCompletionRequest(
            repo_url=repo_url,
            type="github",
            messages=[ChatMessage(
                role="user",
                content=generate_wiki_structure_prompt(
                    owner=owner,
                    repo=repo,
                    file_tree=file_tree,
                    readme_content=readme_content
                )
            )],
            provider="google",  # Default provider
            model=None  # Will use default model
        )

        # For now, use a mock response to test the pipeline
        # TODO: Fix the model integration issue
        ai_response = """<wiki_structure>
  <title>AutoDoc Wiki</title>
  <description>Comprehensive documentation for the AutoDoc repository - an AI-powered tool for automatically generating beautiful, interactive wikis for GitHub, GitLab, and BitBucket repositories</description>
  <sections>
    <section id="overview">
      <title>Overview</title>
      <pages>
        <page_ref>project-overview</page_ref>
        <page_ref>quick-start</page_ref>
      </pages>
    </section>
    <section id="architecture">
      <title>System Architecture</title>
      <pages>
        <page_ref>system-architecture</page_ref>
        <page_ref>data-flow</page_ref>
      </pages>
    </section>
    <section id="features">
      <title>Core Features</title>
      <pages>
        <page_ref>wiki-generation</page_ref>
        <page_ref>ask-feature</page_ref>
      </pages>
    </section>
    <section id="frontend">
      <title>Frontend Components</title>
      <pages>
        <page_ref>react-components</page_ref>
      </pages>
    </section>
    <section id="backend">
      <title>Backend Systems</title>
      <pages>
        <page_ref>api-server</page_ref>
        <page_ref>rag-system</page_ref>
      </pages>
    </section>
    <section id="deployment">
      <title>Deployment</title>
      <pages>
        <page_ref>docker-setup</page_ref>
      </pages>
    </section>
  </sections>
  <pages>
    <page id="project-overview">
      <title>Project Overview</title>
      <description>Introduction to AutoDoc and its main features</description>
      <importance>high</importance>
      <relevant_files>
        <file_path>README.md</file_path>
      </relevant_files>
      <related_pages>
        <related>quick-start</related>
      </related_pages>
      <parent_section>overview</parent_section>
    </page>
    <page id="quick-start">
      <title>Quick Start Guide</title>
      <description>Step-by-step guide to get AutoDoc running</description>
      <importance>high</importance>
      <relevant_files>
        <file_path>README.md</file_path>
        <file_path>docker-compose.yml</file_path>
      </relevant_files>
      <related_pages>
        <related>project-overview</related>
      </related_pages>
      <parent_section>overview</parent_section>
    </page>
    <page id="system-architecture">
      <title>System Architecture</title>
      <description>Overall system design and component relationships</description>
      <importance>high</importance>
      <relevant_files>
        <file_path>api/main.py</file_path>
        <file_path>src/app/layout.tsx</file_path>
      </relevant_files>
      <related_pages>
        <related>data-flow</related>
      </related_pages>
      <parent_section>architecture</parent_section>
    </page>
    <page id="data-flow">
      <title>Data Flow</title>
      <description>How data flows through the system</description>
      <importance>medium</importance>
      <relevant_files>
        <file_path>api/data_pipeline.py</file_path>
        <file_path>api/rag.py</file_path>
      </relevant_files>
      <related_pages>
        <related>system-architecture</related>
      </related_pages>
      <parent_section>architecture</parent_section>
    </page>
    <page id="wiki-generation">
      <title>Wiki Generation</title>
      <description>How AutoDoc generates wikis from repositories</description>
      <importance>high</importance>
      <relevant_files>
        <file_path>api/simple_chat.py</file_path>
        <file_path>api/rag.py</file_path>
      </relevant_files>
      <related_pages>
        <related>ask-feature</related>
      </related_pages>
      <parent_section>features</parent_section>
    </page>
    <page id="ask-feature">
      <title>Ask Feature</title>
      <description>Interactive Q&A with repository using RAG</description>
      <importance>medium</importance>
      <relevant_files>
        <file_path>src/components/Ask.tsx</file_path>
        <file_path>api/rag.py</file_path>
      </relevant_files>
      <related_pages>
        <related>wiki-generation</related>
      </related_pages>
      <parent_section>features</parent_section>
    </page>
    <page id="react-components">
      <title>React Components</title>
      <description>Frontend React components and their functionality</description>
      <importance>medium</importance>
      <relevant_files>
        <file_path>src/components/Ask.tsx</file_path>
        <file_path>src/components/Mermaid.tsx</file_path>
        <file_path>src/components/WikiTreeView.tsx</file_path>
      </relevant_files>
      <related_pages>
        <related>api-server</related>
      </related_pages>
      <parent_section>frontend</parent_section>
    </page>
    <page id="api-server">
      <title>API Server</title>
      <description>Backend FastAPI server implementation</description>
      <importance>high</importance>
      <relevant_files>
        <file_path>api/main.py</file_path>
        <file_path>api/api.py</file_path>
        <file_path>api/simple_chat.py</file_path>
      </relevant_files>
      <related_pages>
        <related>rag-system</related>
      </related_pages>
      <parent_section>backend</parent_section>
    </page>
    <page id="rag-system">
      <title>RAG System</title>
      <description>Retrieval Augmented Generation implementation</description>
      <importance>high</importance>
      <relevant_files>
        <file_path>api/rag.py</file_path>
        <file_path>api/data_pipeline.py</file_path>
      </relevant_files>
      <related_pages>
        <related>api-server</related>
      </related_pages>
      <parent_section>backend</parent_section>
    </page>
    <page id="docker-setup">
      <title>Docker Setup</title>
      <description>Docker configuration and deployment</description>
      <importance>medium</importance>
      <relevant_files>
        <file_path>Dockerfile</file_path>
        <file_path>docker-compose.yml</file_path>
      </relevant_files>
      <related_pages>
        <related>project-overview</related>
      </related_pages>
      <parent_section>deployment</parent_section>
    </page>
  </pages>
</wiki_structure>"""

        logger.info(f"Using mock AI response for testing. Length: {len(ai_response)}")

        # TODO: Uncomment this when model integration is fixed
        # # Call the model to get the complete AI response
        # model_response = await chat_completions(chat_request)
        #
        # # Extract the content from the JSONResponse
        # # The chat_completions function returns JSONResponse(content={"role": "assistant", "content": full_response})
        # if hasattr(model_response, 'body'):
        #     # Handle JSONResponse body
        #     body_content = model_response.body
        #     if isinstance(body_content, bytes):
        #         body_content = body_content.decode('utf-8')
        #
        #     # Parse as JSON to extract content
        #     try:
        #         import json
        #         json_data = json.loads(body_content)
        #         ai_response = json_data.get('content', '')
        #         logger.info(f"Extracted content from JSON response: {ai_response[:100]}...")
        #     except (json.JSONDecodeError, KeyError) as e:
        #         logger.error(f"Failed to parse JSON response: {e}")
        #         ai_response = body_content
        # else:
        #     # Fallback for other response types
        #     ai_response = str(model_response)

        logger.info(f"AI response length: {len(ai_response)}")
        logger.info(f"First 200 chars of AI response: {ai_response[:200]}")

        # Check if response is empty
        if not ai_response or ai_response.strip() == "":
            logger.error("AI response is empty - this indicates an issue with the model call")
            raise ValueError("AI response is empty")

        # Clean up the response to extract XML (ensure ai_response is string)
        ai_response = str(ai_response)
        ai_response = re.sub(r'^```(?:xml)?\s*', '', ai_response, flags=re.IGNORECASE)
        ai_response = re.sub(r'```\s*$', '', ai_response, flags=re.IGNORECASE)
        match = re.search(r"<wiki_structure>[\s\S]*?</wiki_structure>", ai_response, re.MULTILINE)

        if not match:
            logger.error(f"No valid XML structure found in AI response. Response length: {len(ai_response)}")
            logger.error(f"First 500 chars of response: {ai_response[:500]}")
            raise ValueError("No valid XML structure found in AI response")

        xmlMatch = match.group(0)
        xmlText = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', xmlMatch)
        logger.info(f"Successfully extracted XML structure with length: {len(xmlText)}")
        logger.info(f"First 200 chars of XML: {xmlText[:200]}")
        
        # xml_text is your XML string
        root = ET.fromstring(xmlText)
        title_el = root.find('title')
        description_el = root.find('description')
        pages_els = root.findall('.//page')
        title = title_el.text if title_el is not None else ''
        description = description_el.text if description_el is not None else ''
        logger.info(f"The pages are {pages_els}")

        pages = []
        # TODO: Add retry ability
        for page_el in pages_els:
            id_ = page_el.get('id', f'page-{len(pages) + 1}')
            title_el = page_el.find('title')
            importance_el = page_el.find('importance')
            file_path_els = page_el.findall('.//file_path')
            related_els = page_el.findall('.//related')
            title = title_el.text if title_el is not None else ''
            importance = 'medium'
            if importance_el is not None:
                if importance_el.text == 'high':
                    importance = 'high'
                elif importance_el.text == 'medium':
                    importance = 'medium'
                else:
                    importance = 'low'
            file_paths = [el.text for el in file_path_els if el.text]
            related_pages = [el.text for el in related_els if el.text]
            pages.append({
                'id': id_,
                'title': title,
                'content': '',  # Will be generated later
                'filePaths': file_paths,
                'importance': importance,
                'relatedPages': related_pages
            })
        sections = []
        root_sections = []
        sections_els = root.findall('.//section')
        if sections_els:
            for section_el in sections_els:
                id_ = section_el.get('id', f'section-{len(sections) + 1}')
                title_el = section_el.find('title')
                page_ref_els = section_el.findall('page_ref')
                section_ref_els = section_el.findall('section_ref')
                title = title_el.text if title_el is not None else ''
                pages = [el.text for el in page_ref_els if el.text]
                subsections = [el.text for el in section_ref_els if el.text]
                section = {
                    'id': id_,
                    'title': title,
                    'pages': pages,
                    'subsections': subsections if subsections else None
                }
                sections.append(section)
                # Check if this is a root section (not referenced by any other section)
                is_referenced = False
                for other_section in sections_els:
                    other_section_refs = other_section.findall('section_ref')
                    for ref in other_section_refs:
                        if ref.text == id_:
                            is_referenced = True
                if not is_referenced:
                    root_sections.append(id_)
        # Generate wiki structure XML
        wiki_structure_xml = await generate_wiki_structure(owner, repo, file_tree, readme_content)
        logger.info(f"Wiki structure XML generated for {owner}/{repo}")
        # Parse wiki structure
        title, description, pages = parse_wiki_structure(wiki_structure_xml)
        logger.info(f"Parsed wiki structure: {len(pages)} pages")
        # Generate content for each page
        generated_pages = {}
        for page in pages:
            content = await generate_page_content(page, owner, repo)
            generated_pages[page['id']] = {
                'id': page['id'],
                'title': page['title'],
                'content': content,
                'file_paths': page['file_paths'],
                'importance': page['importance'],
                'related_pages': page['related_pages'],
            }
        # Compose result
        result = {
            'wiki_structure': {
                'title': title,
                'description': description,
                'pages': pages
            },
            'generated_pages': generated_pages,
            'repo_url': repo_url
        }
        logger.info(f"Wiki generation complete for {owner}/{repo}")
        return result
    except Exception as e:
        logger.error(f"Error processing Github repository {github_event.repository.full_name}: {str(e)}", exc_info=True)
        return {'error': str(e)}
 

async def get_repo_file_tree(owner: str, repo: str, default_branch: str) -> str:
    """
    Get the file tree of a Github repository.
    Args:
        owner (str): Repository owner
        repo (str): Repository name
        default_branch (str): Default branch name
    Returns:
        str: File tree as a string with one file per line
    """
    try:
        # Get GitHub API token from environment
        token = os.environ.get("GITHUB_API_TOKEN", "")

        # Create headers for GitHub API
        headers = {
            'Accept': 'application/vnd.github.v3+json'
        }
        if token:
            headers['Authorization'] = f'Bearer {token}'

        # Try to get the tree data for common branch names, starting with default_branch
        branches_to_try = [default_branch] if default_branch else []
        # Remove duplicates while preserving order
        branches_to_try = list(dict.fromkeys(branches_to_try))

        tree_data = None
        api_error_details = ''

        # Create SSL context that handles certificate verification issues
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # Create connector with SSL context
        connector = aiohttp.TCPConnector(ssl=ssl_context)

        async with aiohttp.ClientSession(connector=connector) as session:
            for branch in branches_to_try:
                api_url = f"https://api.github.com/repos/{owner}/{repo}/git/trees/{branch}?recursive=1"
                logger.info(f"Fetching repository structure from branch: {branch}")

                try:
                    async with session.get(api_url, headers=headers) as response:
                        if response.status == 200:
                            tree_data = await response.json()
                            logger.info('Successfully fetched repository structure')
                            break
                        else:
                            error_data = await response.text()
                            api_error_details = f"Status: {response.status}, Response: {error_data}"
                            logger.error(f"Error fetching repository structure: {api_error_details}")
                except Exception as err:
                    logger.error(f"Network error fetching branch {branch}: {err}")
                    continue

        if not tree_data or 'tree' not in tree_data:
            if api_error_details:
                logger.error(f"Could not fetch repository structure. API Error: {api_error_details}")
                return ""
            else:
                logger.error('Could not fetch repository structure. Repository might not exist, be empty or private.')
                return ""

        # Convert tree data to a string representation (filter for files only)
        file_tree = tree_data['tree']
        file_paths = [
            item['path'] for item in file_tree
            if item.get('type') == 'blob'  # 'blob' represents files, 'tree' represents directories
        ]

        file_tree_string = '\n'.join(file_paths)
        logger.info(f"Successfully generated file tree with {len(file_paths)} files")
        return file_tree_string

    except Exception as e:
        logger.error(f"Error getting file tree for {owner}/{repo}: {str(e)}", exc_info=True)
        return ""
 

async def get_repo_readme(owner: str, repo: str) -> str:
    """
    Get the README content of a Github repository.
    Args:
        owner (str): Repository owner
        repo (str): Repository name
    Returns:
        str: README content as a string
    """
    try:
        # Get Github API token from environment
        token = os.environ.get("GITHUB_API_TOKEN", "")
        headers = {
            'Accept': 'application/vnd.github.v3+json'
        }
        if token:
            headers["Authorization"] = f"Bearer {token}"
        # Create SSL context that handles certificate verification issues
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # Create connector with SSL context
        connector = aiohttp.TCPConnector(ssl=ssl_context)

        async with aiohttp.ClientSession(connector=connector) as session:
            # Try GitHub API first
            api_url = f"https://api.github.com/repos/{owner}/{repo}/readme"
            async with session.get(api_url, headers=headers) as response:
                if response.status == 200:
                    readme_data = await response.json()
                    # GitHub API returns base64 encoded content
                    import base64
                    return base64.b64decode(readme_data['content']).decode('utf-8')
            # If no README found, return empty string
            logger.warning(f"No README found for repository {owner}/{repo}")
            return ""
    except Exception as e:
        logger.error(f"Error getting README for {owner}/{repo}: {str(e)}", exc_info=True)
        return ""
 

def clone_repo(self, params=None, method='GET', data=None, endpoint=''):
    """
    Make a request to the Github API.
    Args:
        params (dict, optional): Query parameters for the request
        method (str): HTTP method ('GET' or 'POST')
        data (dict, optional): Data to send in the request body (for POST requests)
        endpoint (str): API endpoint to call (e.g., 'repositories/{owner}/{repo}/src')
    Returns:
        dict: JSON response from the Github API
    Raises:
        HTTPError: If the request fails
    """
    url = f"https://github.org/{endpoint}"
    if method == 'GET':
        response = requests.get(url, auth=(self.inputs.sid, self.inputs.password), params=params)
    elif method == 'POST':
        response = requests.post(url, auth=(self.inputs.sid, self.inputs.password), json=data)
    response.raise_for_status()
    return response.json()
 

@app.post("/webhook")
async def github_webhook(request: Request, background_tasks: BackgroundTasks):
    """
    Webhook endpoint for GitHub issue events. This will be the dedicated entry point for GitHub issue events.
    It immediately returns a 202 Accepted response and starts the generation process in the background.
    Args:
        request: The incoming request containing the GitHub webhook payload
        background_tasks: FastAPI's BackgroundTasks for async processing
    Returns:
        A 202 Accepted response indicating the webhook was received and processing has started
    """
    try:
        # Parse the webhook payload
        payload = await request.json()
        # Extract GitHub event type from headers
        github_event = request.headers.get("X-GitHub-Event")
        logger.info(f"Received GitHub webhook event: {github_event}")
        logger.info(f"Request headers: {request.headers}")
        # Log the event
        action = payload.get("action", None)
        logger.info(f"Received GitHub webhook event with action: {action}")
        # Validate HMAC-SHA256 signature
        signature = request.headers.get("X-Hub-Signature")
        if not signature:
            logger.error("Missing HMAC-SHA256 signature in webhook headers")
            raise HTTPException(status_code=400, detail="Missing HMAC-SHA256 signature")
        # secret = os.environ.get("Github_WEBHOOK_SECRET", "")
        # if not secret:
        #     logger.error("Webhook secret not configured in environment variables")
        #     raise HTTPException(status_code=500, detail="Webhook secret not configured")
        # computed_signature = hmac.new(secret.encode(), await request.body(), hashlib.sha256).hexdigest()
        # if not hmac.compare_digest(computed_signature, signature):
        #     logger.error("Invalid HMAC-SHA256 signature")
        #     raise HTTPException(status_code=403, detail="Invalid HMAC-SHA256 signature")
        # Check if this is a GitHub issue event
        if action == "closed" and github_event == "push":
            try:
                # Parse the issue event data
                push_event = GithubPushEvent(**payload)
                logger.info(f"Push event is {push_event}")
                logger.info(f"Processing GitHub push event: {action} for push #{push_event.number}")

                # Add the background task for processing
                background_tasks.add_task(
                    process_github_repository_async,
                    github_event=push_event
                )
                logger.info(f"Background task added for processing repository: {push_event.repository.full_name}")
                return JSONResponse(
                    status_code=202,
                    content={"message": f"Webhook received. Processing repository {push_event.repository.full_name} in background."}
                )
            except Exception as e:
                logger.error(f"Error parsing GitHub issue event: {str(e)}", exc_info=True)
                raise HTTPException(status_code=400, detail=f"Invalid issue event format: {str(e)}")
        else:
            # For other event types, just acknowledge receipt
            logger.info(f"Received unsupported GitHub event with action: {action}")
            return JSONResponse(
                status_code=202,
                content={"message": "Webhook received, but event type is not supported for processing."}
            )
    except json.JSONDecodeError:
        logger.error("Invalid JSON in webhook payload")
        raise HTTPException(status_code=400, detail="Invalid JSON in webhook payload")
    except Exception as e:
        logger.error(f"Error processing webhook: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


if __name__ == "__main__":
    import uvicorn

    # Get port from environment variable or use default
    webhook_port = int(os.environ.get("WEBHOOK_PORT", 8002))

    logger.info(f"Starting GitHub Webhook API on port {webhook_port}")

    # Run the webhook FastAPI app with uvicorn
    uvicorn.run(
        "api.web_hook.github_api:app",
        host="0.0.0.0",
        port=webhook_port,
        reload=True  # Webhooks should be stable, no hot reload needed
    )
